/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package electronmain

import (
	"time"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	lifecycleelectronmain "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/lifecycle/electron-main"
	windowelectronmain "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/window/electron-main"
	windowselectronmain "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/windows/electron-main"
	workspacecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/workspace/common"
)

// UserDataProfilesHandler handles user data profile lifecycle events
type UserDataProfilesHandler struct {
	basecommon.Disposable

	// Services
	userDataProfilesService IUserDataProfilesMainService
	windowsMainService      windowselectronmain.IWindowsMainService

	// Scheduler for cleanup
	cleanupScheduler *basecommon.RunOnceScheduler
}

// NewUserDataProfilesHandler creates a new UserDataProfilesHandler
func NewUserDataProfilesHandler(
	lifecycleMainService lifecycleelectronmain.ILifecycleMainService,
	userDataProfilesService IUserDataProfilesMainService,
	windowsMainService windowselectronmain.IWindowsMainService,
) *UserDataProfilesHandler {
	handler := &UserDataProfilesHandler{
		Disposable:              *basecommon.NewDisposable(),
		userDataProfilesService: userDataProfilesService,
		windowsMainService:      windowsMainService,
	}

	// Register lifecycle event handlers
	handler.Register(lifecycleMainService.OnWillLoadWindow().Subscribe(func(e *lifecycleelectronmain.WindowLoadEvent) {
		if e.Reason == windowelectronmain.LoadReasonLoad {
			handler.unsetProfileForWorkspace(e.Window)
		}
	}))

	handler.Register(lifecycleMainService.OnBeforeCloseWindow().Subscribe(func(window windowelectronmain.ICodeWindow) {
		handler.unsetProfileForWorkspace(window)
	}))

	// Schedule cleanup of empty window associations after 30 seconds
	handler.cleanupScheduler = basecommon.NewRunOnceScheduler(
		func() {
			handler.cleanUpEmptyWindowAssociations()
		},
		30*time.Second,
	)
	handler.Register(handler.cleanupScheduler)
	handler.cleanupScheduler.Schedule()

	return handler
}

// unsetProfileForWorkspace unsets the profile for a workspace when a window is closed or loaded
func (h *UserDataProfilesHandler) unsetProfileForWorkspace(window windowelectronmain.ICodeWindow) {
	go func() {
		workspace := h.getWorkspace(window)
		profile := h.userDataProfilesService.GetProfileForWorkspace(workspace)

		if profile != nil && profile.IsTransient != nil && *profile.IsTransient {
			h.userDataProfilesService.UnsetWorkspace(workspace, *profile.IsTransient)

			if *profile.IsTransient {
				err := h.userDataProfilesService.CleanUpTransientProfiles()
				if err != nil {
					// Log error in a real implementation
					_ = err
				}
			}
		}
	}()
}

// getWorkspace gets the workspace identifier for a window
func (h *UserDataProfilesHandler) getWorkspace(window windowelectronmain.ICodeWindow) workspacecommon.IAnyWorkspaceIdentifier {
	openedWorkspace := window.OpenedWorkspace()
	if openedWorkspace != nil {
		return openedWorkspace
	}

	// Convert backup path to workspace identifier
	backupPath := window.BackupPath()
	isExtensionDevelopmentHost := window.IsExtensionDevelopmentHost()

	if backupPath != nil {
		return workspacecommon.ToWorkspaceIdentifier(*backupPath, isExtensionDevelopmentHost)
	}

	// Return empty workspace identifier if no backup path
	return &workspacecommon.IEmptyWorkspaceIdentifier{
		IBaseWorkspaceIdentifier: workspacecommon.IBaseWorkspaceIdentifier{
			ID: "empty",
		},
	}
}

// cleanUpEmptyWindowAssociations cleans up empty window associations that are no longer open
func (h *UserDataProfilesHandler) cleanUpEmptyWindowAssociations() {
	associatedEmptyWindows := h.userDataProfilesService.GetAssociatedEmptyWindows()
	if len(associatedEmptyWindows) == 0 {
		return
	}

	// Get all currently opened workspaces
	openedWorkspaces := make([]workspacecommon.IAnyWorkspaceIdentifier, 0)
	windows := h.windowsMainService.GetWindows()
	for _, window := range windows {
		workspace := h.getWorkspace(window)
		openedWorkspaces = append(openedWorkspaces, workspace)
	}

	// Check each associated empty window
	for _, associatedEmptyWindow := range associatedEmptyWindows {
		// Check if this empty window is still open
		isStillOpen := false
		for _, openedWorkspace := range openedWorkspaces {
			if h.workspaceIDsMatch(openedWorkspace, &associatedEmptyWindow) {
				isStillOpen = true
				break
			}
		}

		// If not still open, unset the workspace association
		if !isStillOpen {
			h.userDataProfilesService.UnsetWorkspace(&associatedEmptyWindow, false)
		}
	}
}

// workspaceIDsMatch checks if two workspace identifiers have the same ID
func (h *UserDataProfilesHandler) workspaceIDsMatch(workspace1, workspace2 workspacecommon.IAnyWorkspaceIdentifier) bool {
	if workspace1 == nil || workspace2 == nil {
		return false
	}

	// Get IDs from both workspaces
	id1 := h.getWorkspaceID(workspace1)
	id2 := h.getWorkspaceID(workspace2)

	return id1 == id2
}

// getWorkspaceID extracts the ID from a workspace identifier
func (h *UserDataProfilesHandler) getWorkspaceID(workspace workspacecommon.IAnyWorkspaceIdentifier) string {
	if workspace == nil {
		return ""
	}

	// Handle different workspace types
	switch w := workspace.(type) {
	case *workspacecommon.IWorkspaceIdentifier:
		return w.ID
	case *workspacecommon.ISingleFolderWorkspaceIdentifier:
		return w.ID
	case *workspacecommon.IEmptyWorkspaceIdentifier:
		return w.ID
	default:
		// Try to get ID through interface method if available
		if idGetter, ok := workspace.(interface{ GetID() string }); ok {
			return idGetter.GetID()
		}
		return ""
	}
}
