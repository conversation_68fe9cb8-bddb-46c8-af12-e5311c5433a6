/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package electronmain

import (
	"fmt"
	"time"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	storagecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/parts/storage/common"
	logcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/log/common"
	storageelectronmain "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/storage/electron-main"
	userDataProfileCommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/userDataProfile/common"
	workspacecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/workspace/common"
)

// IBaseSerializableStorageRequest represents a base serializable storage request
type IBaseSerializableStorageRequest struct {
	// Profile to correlate storage. Only used when no
	// workspace is provided. Can be undefined to denote
	// application scope.
	Profile *userDataProfileCommon.IUserDataProfile `json:"profile,omitempty"`

	// Workspace to correlate storage. Can be undefined to
	// denote application or profile scope depending on profile.
	Workspace workspacecommon.IAnyWorkspaceIdentifier `json:"workspace,omitempty"`

	// Additional payload for the request to perform.
	Payload interface{} `json:"payload,omitempty"`
}

// ProfileStorageChangesListenerChannel implements IServerChannel for profile storage changes
type ProfileStorageChangesListenerChannel struct {
	basecommon.Disposable

	// Services
	storageMainService      storageelectronmain.IStorageMainService
	userDataProfilesService userDataProfileCommon.IUserDataProfilesService
	logService              logcommon.ILogService

	// Events
	onDidChange basecommon.Emitter[*userDataProfileCommon.IProfileStorageChanges]

	// State
	disposable *basecommon.MutableDisposable
}

// NewProfileStorageChangesListenerChannel creates a new ProfileStorageChangesListenerChannel
func NewProfileStorageChangesListenerChannel(
	storageMainService storageelectronmain.IStorageMainService,
	userDataProfilesService userDataProfileCommon.IUserDataProfilesService,
	logService logcommon.ILogService,
) *ProfileStorageChangesListenerChannel {
	channel := &ProfileStorageChangesListenerChannel{
		Disposable:              *basecommon.NewDisposable(),
		storageMainService:      storageMainService,
		userDataProfilesService: userDataProfilesService,
		logService:              logService,
		disposable:              basecommon.NewMutableDisposable(),
	}

	// Create emitter with lazy listener registration
	channel.onDidChange = *basecommon.NewEmitter[*userDataProfileCommon.IProfileStorageChanges](
		basecommon.EmitterOptions{
			OnWillAddFirstListener: func() {
				// Start listening to profile storage changes only when someone is listening
				channel.disposable.SetValue(channel.registerStorageChangeListeners())
			},
			OnDidRemoveLastListener: func() {
				// Stop listening to profile storage changes when no one is listening
				channel.disposable.SetValue(nil)
			},
		},
	)

	channel.Register(channel.disposable)
	channel.Register(&channel.onDidChange)

	return channel
}

// registerStorageChangeListeners registers listeners for storage changes
func (c *ProfileStorageChangesListenerChannel) registerStorageChangeListeners() basecommon.IDisposable {
	c.logService.Debug("ProfileStorageChangesListenerChannel#registerStorageChangeListeners")

	disposables := basecommon.NewDisposableStore()

	// Listen to application storage changes with debouncing
	applicationStorageListener := basecommon.Debounce(
		c.storageMainService.ApplicationStorage().OnDidChangeStorage(),
		100*time.Millisecond,
		func(e1, e2 *storageelectronmain.IStorageChangeEvent) *storageelectronmain.IStorageChangeEvent {
			// Merge storage change events by combining keys
			return e2 // For simplicity, just return the latest event
		},
	)

	disposables.Add(applicationStorageListener.Subscribe(func(e *storageelectronmain.IStorageChangeEvent) {
		c.onDidChangeApplicationStorage([]string{e.Key})
	}))

	// Listen to profile storage changes with debouncing
	profileStorageListener := basecommon.Debounce(
		c.storageMainService.OnDidChangeProfileStorage(),
		100*time.Millisecond,
		func(e1, e2 *storageelectronmain.IProfileStorageChangeEvent) *storageelectronmain.IProfileStorageChangeEvent {
			// For simplicity, just return the latest event
			return e2
		},
	)

	disposables.Add(profileStorageListener.Subscribe(func(e *storageelectronmain.IProfileStorageChangeEvent) {
		// Convert to our internal format
		// Note: We need to convert the profile type properly
		profile := &userDataProfileCommon.IUserDataProfile{
			ID:   e.Profile.ID,
			Name: e.Profile.Name,
			// Add other fields as needed
		}

		changes := map[string]*ProfileStorageChange{
			e.Profile.ID: {
				Profile: profile,
				Keys:    []string{e.Key},
				Storage: e.Storage,
			},
		}
		c.onDidChangeProfileStorage(changes)
	}))

	return disposables
}

// ProfileStorageChange represents a profile storage change
type ProfileStorageChange struct {
	Profile *userDataProfileCommon.IUserDataProfile
	Keys    []string
	Storage storageelectronmain.IStorageMain
}

// onDidChangeApplicationStorage handles application storage changes
func (c *ProfileStorageChangesListenerChannel) onDidChangeApplicationStorage(keys []string) {
	targetChangedProfiles := make([]*userDataProfileCommon.IUserDataProfile, 0)
	profileStorageValueChanges := make([]*userDataProfileCommon.IProfileStorageValueChanges, 0)

	// Check if TARGET_KEY is in the changes
	filteredKeys := make([]string, 0)
	for _, key := range keys {
		if key == storagecommon.TARGET_KEY {
			targetChangedProfiles = append(targetChangedProfiles, c.userDataProfilesService.DefaultProfile())
		} else {
			filteredKeys = append(filteredKeys, key)
		}
	}

	// Process remaining keys
	if len(filteredKeys) > 0 {
		storage, ok := c.storageMainService.ApplicationStorage().Storage().(storagecommon.IStorage)
		if !ok {
			c.logService.Error("Failed to cast application storage to IStorage")
			return
		}
		keyTargets := storagecommon.LoadKeyTargets(storage)
		changes := make([]*storagecommon.IStorageValueChangeEvent, 0)

		for _, key := range filteredKeys {
			target := keyTargets[key]
			changes = append(changes, &storagecommon.IStorageValueChangeEvent{
				IStorageChangeEvent: storagecommon.IStorageChangeEvent{
					Scope:  storagecommon.StorageScopeApplication,
					Key:    key,
					Value:  nil, // We don't have the value in this context
					Target: target,
				},
				External: false,
			})
		}

		profileStorageValueChanges = append(profileStorageValueChanges, &userDataProfileCommon.IProfileStorageValueChanges{
			Profile: c.userDataProfilesService.DefaultProfile(),
			Changes: changes,
		})
	}

	c.triggerEvents(targetChangedProfiles, profileStorageValueChanges)
}

// onDidChangeProfileStorage handles profile storage changes
func (c *ProfileStorageChangesListenerChannel) onDidChangeProfileStorage(changes map[string]*ProfileStorageChange) {
	targetChangedProfiles := make([]*userDataProfileCommon.IUserDataProfile, 0)
	profileStorageValueChanges := make([]*userDataProfileCommon.IProfileStorageValueChanges, 0)

	for _, profileChanges := range changes {
		// Check if TARGET_KEY is in the changes
		filteredKeys := make([]string, 0)
		for _, key := range profileChanges.Keys {
			if key == storagecommon.TARGET_KEY {
				targetChangedProfiles = append(targetChangedProfiles, profileChanges.Profile)
			} else {
				filteredKeys = append(filteredKeys, key)
			}
		}

		// Process remaining keys
		if len(filteredKeys) > 0 {
			storage, ok := profileChanges.Storage.Storage().(storagecommon.IStorage)
			if !ok {
				c.logService.Error("Failed to cast profile storage to IStorage")
				continue
			}
			keyTargets := storagecommon.LoadKeyTargets(storage)
			changes := make([]*storagecommon.IStorageValueChangeEvent, 0)

			for _, key := range filteredKeys {
				target := keyTargets[key]
				changes = append(changes, &storagecommon.IStorageValueChangeEvent{
					IStorageChangeEvent: storagecommon.IStorageChangeEvent{
						Scope:  storagecommon.StorageScopeProfile,
						Key:    key,
						Value:  nil, // We don't have the value in this context
						Target: target,
					},
					External: false,
				})
			}

			profileStorageValueChanges = append(profileStorageValueChanges, &userDataProfileCommon.IProfileStorageValueChanges{
				Profile: profileChanges.Profile,
				Changes: changes,
			})
		}
	}

	c.triggerEvents(targetChangedProfiles, profileStorageValueChanges)
}

// triggerEvents triggers the profile storage changes event
func (c *ProfileStorageChangesListenerChannel) triggerEvents(
	targetChanges []*userDataProfileCommon.IUserDataProfile,
	valueChanges []*userDataProfileCommon.IProfileStorageValueChanges,
) {
	if len(targetChanges) > 0 || len(valueChanges) > 0 {
		c.onDidChange.Fire(&userDataProfileCommon.IProfileStorageChanges{
			ValueChanges:  valueChanges,
			TargetChanges: targetChanges,
		})
	}
}

// Listen implements IServerChannel.Listen
func (c *ProfileStorageChangesListenerChannel) Listen(
	ctx interface{},
	event string,
	arg *IBaseSerializableStorageRequest,
) basecommon.Event[interface{}] {
	switch event {
	case "onDidChange":
		// Create a new emitter that converts the typed event to interface{}
		emitter := basecommon.NewEmitter[interface{}]()

		// Subscribe to the typed event and forward as interface{}
		c.onDidChange.Event().Subscribe(func(changes *userDataProfileCommon.IProfileStorageChanges) {
			emitter.Fire(changes)
		})

		return emitter.Event()
	default:
		panic(fmt.Sprintf("[ProfileStorageChangesListenerChannel] Event not found: %s", event))
	}
}

// Call implements IServerChannel.Call
func (c *ProfileStorageChangesListenerChannel) Call(
	ctx interface{},
	command string,
	arg interface{},
) (interface{}, error) {
	// This channel only supports events, not calls
	return nil, fmt.Errorf("[ProfileStorageChangesListenerChannel] Call not supported: %s", command)
}
