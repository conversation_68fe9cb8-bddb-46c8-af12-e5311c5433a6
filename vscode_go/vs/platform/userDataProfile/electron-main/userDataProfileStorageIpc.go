/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package main

import (
	"fmt"
	"time"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	logcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/log/common"
	storagecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/storage/common"
	storageelectronmain "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/storage/electron-main"
	userDataProfileCommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/userDataProfile/common"
)

// ProfileStorageChangesListenerChannel implements IServerChannel for profile storage changes
type ProfileStorageChangesListenerChannel struct {
	basecommon.Disposable

	// Services
	storageMainService      storageelectronmain.IStorageMainService
	userDataProfilesService userDataProfileCommon.IUserDataProfilesService
	logService              logcommon.ILogService

	// Events
	onDidChange basecommon.Emitter[*userDataProfileCommon.IProfileStorageChanges]

	// State
	disposable *basecommon.MutableDisposable[basecommon.IDisposable]
}

// NewProfileStorageChangesListenerChannel creates a new ProfileStorageChangesListenerChannel
func NewProfileStorageChangesListenerChannel(
	storageMainService storageelectronmain.IStorageMainService,
	userDataProfilesService userDataProfileCommon.IUserDataProfilesService,
	logService logcommon.ILogService,
) *ProfileStorageChangesListenerChannel {
	channel := &ProfileStorageChangesListenerChannel{
		Disposable:              basecommon.NewDisposable(),
		storageMainService:      storageMainService,
		userDataProfilesService: userDataProfilesService,
		logService:              logService,
		disposable:              basecommon.NewMutableDisposable[basecommon.IDisposable](),
	}

	// Create emitter with lazy listener registration
	channel.onDidChange = basecommon.NewEmitterWithOptions[*userDataProfileCommon.IProfileStorageChanges](
		&basecommon.EmitterOptions{
			OnWillAddFirstListener: func() {
				// Start listening to profile storage changes only when someone is listening
				channel.disposable.SetValue(channel.registerStorageChangeListeners())
			},
			OnDidRemoveLastListener: func() {
				// Stop listening to profile storage changes when no one is listening
				channel.disposable.Clear()
			},
		},
	)

	channel.Register(channel.disposable)
	channel.Register(&channel.onDidChange)

	return channel
}

// registerStorageChangeListeners registers listeners for storage changes
func (c *ProfileStorageChangesListenerChannel) registerStorageChangeListeners() basecommon.IDisposable {
	c.logService.Debug("ProfileStorageChangesListenerChannel#registerStorageChangeListeners")

	disposables := basecommon.NewDisposableStore()

	// Listen to application storage changes with debouncing
	applicationStorageListener := basecommon.DebounceEvent(
		c.storageMainService.ApplicationStorage().OnDidChangeStorage(),
		func(keys []string, e *storagecommon.IStorageChangeEvent) []string {
			if keys == nil {
				keys = make([]string, 0)
			}
			keys = append(keys, e.Key)
			return keys
		},
		100*time.Millisecond,
	)

	disposables.Add(basecommon.ToDisposable(func() {
		applicationStorageListener(func(keys []string) {
			c.onDidChangeApplicationStorage(keys)
		})
	}))

	// Listen to profile storage changes with debouncing
	profileStorageListener := basecommon.DebounceEvent(
		c.storageMainService.OnDidChangeProfileStorage(),
		func(changes map[string]*ProfileStorageChange, e *storageelectronmain.IProfileStorageChangeEvent) map[string]*ProfileStorageChange {
			if changes == nil {
				changes = make(map[string]*ProfileStorageChange)
			}

			profileChanges, exists := changes[e.Profile.ID]
			if !exists {
				profileChanges = &ProfileStorageChange{
					Profile: e.Profile,
					Keys:    make([]string, 0),
					Storage: e.Storage,
				}
				changes[e.Profile.ID] = profileChanges
			}
			profileChanges.Keys = append(profileChanges.Keys, e.Key)
			return changes
		},
		100*time.Millisecond,
	)

	disposables.Add(basecommon.ToDisposable(func() {
		profileStorageListener(func(changes map[string]*ProfileStorageChange) {
			c.onDidChangeProfileStorage(changes)
		})
	}))

	return disposables
}

// ProfileStorageChange represents a profile storage change
type ProfileStorageChange struct {
	Profile *userDataProfileCommon.IUserDataProfile
	Keys    []string
	Storage storageelectronmain.IStorageMain
}

// onDidChangeApplicationStorage handles application storage changes
func (c *ProfileStorageChangesListenerChannel) onDidChangeApplicationStorage(keys []string) {
	targetChangedProfiles := make([]*userDataProfileCommon.IUserDataProfile, 0)
	profileStorageValueChanges := make([]*userDataProfileCommon.IProfileStorageValueChanges, 0)

	// Check if TARGET_KEY is in the changes
	filteredKeys := make([]string, 0)
	for _, key := range keys {
		if key == storagecommon.TargetKey {
			targetChangedProfiles = append(targetChangedProfiles, c.userDataProfilesService.DefaultProfile())
		} else {
			filteredKeys = append(filteredKeys, key)
		}
	}

	// Process remaining keys
	if len(filteredKeys) > 0 {
		keyTargets := storagecommon.LoadKeyTargets(c.storageMainService.ApplicationStorage().Storage())
		changes := make([]*userDataProfileCommon.IStorageValueChange, 0)

		for _, key := range filteredKeys {
			target := keyTargets[key]
			changes = append(changes, &userDataProfileCommon.IStorageValueChange{
				Key:    key,
				Scope:  storagecommon.StorageScopeProfile,
				Target: target,
			})
		}

		profileStorageValueChanges = append(profileStorageValueChanges, &userDataProfileCommon.IProfileStorageValueChanges{
			Profile: c.userDataProfilesService.DefaultProfile(),
			Changes: changes,
		})
	}

	c.triggerEvents(targetChangedProfiles, profileStorageValueChanges)
}

// onDidChangeProfileStorage handles profile storage changes
func (c *ProfileStorageChangesListenerChannel) onDidChangeProfileStorage(changes map[string]*ProfileStorageChange) {
	targetChangedProfiles := make([]*userDataProfileCommon.IUserDataProfile, 0)
	profileStorageValueChanges := make([]*userDataProfileCommon.IProfileStorageValueChanges, 0)

	for _, profileChanges := range changes {
		// Check if TARGET_KEY is in the changes
		filteredKeys := make([]string, 0)
		for _, key := range profileChanges.Keys {
			if key == storagecommon.TargetKey {
				targetChangedProfiles = append(targetChangedProfiles, profileChanges.Profile)
			} else {
				filteredKeys = append(filteredKeys, key)
			}
		}

		// Process remaining keys
		if len(filteredKeys) > 0 {
			keyTargets := storagecommon.LoadKeyTargets(profileChanges.Storage.Storage())
			changes := make([]*userDataProfileCommon.IStorageValueChange, 0)

			for _, key := range filteredKeys {
				target := keyTargets[key]
				changes = append(changes, &userDataProfileCommon.IStorageValueChange{
					Key:    key,
					Scope:  storagecommon.StorageScopeProfile,
					Target: target,
				})
			}

			profileStorageValueChanges = append(profileStorageValueChanges, &userDataProfileCommon.IProfileStorageValueChanges{
				Profile: profileChanges.Profile,
				Changes: changes,
			})
		}
	}

	c.triggerEvents(targetChangedProfiles, profileStorageValueChanges)
}

// triggerEvents triggers the profile storage changes event
func (c *ProfileStorageChangesListenerChannel) triggerEvents(
	targetChanges []*userDataProfileCommon.IUserDataProfile,
	valueChanges []*userDataProfileCommon.IProfileStorageValueChanges,
) {
	if len(targetChanges) > 0 || len(valueChanges) > 0 {
		c.onDidChange.Fire(&userDataProfileCommon.IProfileStorageChanges{
			ValueChanges:  valueChanges,
			TargetChanges: targetChanges,
		})
	}
}

// Listen implements IServerChannel.Listen
func (c *ProfileStorageChangesListenerChannel) Listen(
	ctx interface{},
	event string,
	arg *storagecommon.IBaseSerializableStorageRequest,
) basecommon.Event[interface{}] {
	switch event {
	case "onDidChange":
		return func(listener func(interface{})) {
			c.onDidChange.Event(func(changes *userDataProfileCommon.IProfileStorageChanges) {
				listener(changes)
			})
		}
	default:
		panic(fmt.Sprintf("[ProfileStorageChangesListenerChannel] Event not found: %s", event))
	}
}

// Call implements IServerChannel.Call
func (c *ProfileStorageChangesListenerChannel) Call(ctx interface{}, command string, args ...interface{}) (interface{}, error) {
	return nil, fmt.Errorf("Call not found: %s", command)
}
