/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package main

import (
	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	windowelectronmain "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/window/electron-main"
	workspacecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/workspace/common"
)

// ShutdownReason represents the reason for application shutdown
type ShutdownReason int

const (
	// ShutdownReasonQuit - The application exits normally
	ShutdownReasonQuit ShutdownReason = 1
	// ShutdownReasonKill - The application exits abnormally and is being killed with an exit code
	ShutdownReasonKill ShutdownReason = 2
)

// ShutdownEvent represents a shutdown event
type ShutdownEvent struct {
	// Reason - More details why the application is shutting down
	Reason ShutdownReason
	// Join allows to join the shutdown. The promise can be a long running operation but it will block the application from closing
	Join func(id string, promise <-chan error)
}

// WindowLoadEvent represents a window load event
type WindowLoadEvent struct {
	// Window - The window that is loaded to a new workspace
	Window windowelectronmain.ICodeWindow
	// Workspace - The workspace the window is loaded into
	Workspace workspacecommon.IAnyWorkspaceIdentifier
	// Reason - More details why the window loads to a new workspace
	Reason windowelectronmain.LoadReason
}

// LifecycleMainPhase represents the lifecycle phase
type LifecycleMainPhase int

const (
	// LifecycleMainPhaseStarting - Starting phase
	LifecycleMainPhaseStarting LifecycleMainPhase = 1
	// LifecycleMainPhaseReady - Ready phase
	LifecycleMainPhaseReady LifecycleMainPhase = 2
	// LifecycleMainPhaseAfterWindowOpen - After window open phase
	LifecycleMainPhaseAfterWindowOpen LifecycleMainPhase = 3
	// LifecycleMainPhaseEventuallyReady - Eventually ready phase
	LifecycleMainPhaseEventuallyReady LifecycleMainPhase = 4
)

// IRelaunchOptions represents relaunch options
type IRelaunchOptions struct {
	AddArgs    []string
	RemoveArgs []string
}

// IRelaunchHandler allows a handler to deal with relaunching the application
type IRelaunchHandler interface {
	// HandleRelaunch allows a handler to deal with relaunching the application.
	// The return value indicates if the relaunch is handled or not.
	HandleRelaunch(options *IRelaunchOptions) bool
}

// ILifecycleMainService represents the main lifecycle service interface
type ILifecycleMainService interface {
	// WasRestarted - Will be true if the program was restarted (e.g. due to explicit request or update)
	WasRestarted() bool

	// QuitRequested - Will be true if the program was requested to quit
	QuitRequested() bool

	// Phase - A flag indicating in what phase of the lifecycle we currently are
	Phase() LifecycleMainPhase
	SetPhase(phase LifecycleMainPhase)

	// OnBeforeShutdown - An event that fires when the application is about to shutdown before any window is closed.
	// The shutdown can still be prevented by any window that vetos this event.
	OnBeforeShutdown() basecommon.Event[interface{}]

	// OnWillShutdown - An event that fires after the onBeforeShutdown event has been fired and after no window has
	// vetoed the shutdown sequence. At this point listeners are ensured that the application will quit without veto.
	OnWillShutdown() basecommon.Event[*ShutdownEvent]

	// OnWillLoadWindow - An event that fires when a window is loading. This can either be a window opening for the
	// first time or a window reloading or changing to another URL.
	OnWillLoadWindow() basecommon.Event[*WindowLoadEvent]

	// OnBeforeCloseWindow - An event that fires before a window closes. This event is fired after any veto has been dealt
	// with so that listeners know for sure that the window will close without veto.
	OnBeforeCloseWindow() basecommon.Event[windowelectronmain.ICodeWindow]

	// RegisterWindow - Make a ICodeWindow known to the lifecycle main service
	RegisterWindow(window windowelectronmain.ICodeWindow)

	// Reload - Reload a window. All lifecycle event handlers are triggered
	Reload(window windowelectronmain.ICodeWindow, cli interface{}) error

	// Unload - Unload a window for the provided reason. All lifecycle event handlers are triggered
	Unload(window windowelectronmain.ICodeWindow, reason windowelectronmain.UnloadReason) (bool, error)

	// Relaunch - Restart the application with optional arguments
	Relaunch(options *IRelaunchOptions)

	// Quit - Quit the application
	Quit(willRestart bool) error

	// Kill - Kill the application with exit code
	Kill(code int)
}
