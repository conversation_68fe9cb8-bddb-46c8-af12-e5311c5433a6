/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package electronmain

import (
	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	instantiationcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/instantiation/common"
	windowelectronmain "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/window/electron-main"
)

// OpenContext represents the context in which a window is opened
type OpenContext int

const (
	// OpenContextCLI - opening when running from the command line
	OpenContextCLI OpenContext = iota
	// OpenContextDock - macOS only: opening from the dock
	OpenContextDock
	// OpenContextMenu - opening from the main application window
	OpenContextMenu
	// OpenContextDialog - opening from a file or folder dialog
	OpenContextDialog
	// OpenContextDesktop - opening from the OS's UI
	OpenContextDesktop
	// OpenContextAPI - opening through the API
	OpenContextAPI
	// OpenContextLink - opening from a protocol link
	OpenContextLink
)

// IWindowsCountChangedEvent represents an event when the window count changes
type IWindowsCountChangedEvent struct {
	OldCount int `json:"oldCount"`
	NewCount int `json:"newCount"`
}

// IBaseOpenConfiguration represents base open configuration
type IBaseOpenConfiguration struct {
	Context         OpenContext `json:"context"`
	ContextWindowID *int        `json:"contextWindowId"`
}

// IOpenConfiguration represents open configuration
type IOpenConfiguration struct {
	IBaseOpenConfiguration
	CLI                  interface{}   `json:"cli"`               // NativeParsedArgs
	UserEnv              interface{}   `json:"userEnv"`           // IProcessEnvironment
	URIsToOpen           []interface{} `json:"urisToOpen"`        // IWindowOpenable[]
	WaitMarkerFileURI    interface{}   `json:"waitMarkerFileURI"` // URI
	PreferNewWindow      *bool         `json:"preferNewWindow"`
	ForceNewWindow       *bool         `json:"forceNewWindow"`
	ForceNewTabbedWindow *bool         `json:"forceNewTabbedWindow"`
	ForceReuseWindow     *bool         `json:"forceReuseWindow"`
	ForceEmpty           *bool         `json:"forceEmpty"`
	DiffMode             *bool         `json:"diffMode"`
	MergeMode            *bool         `json:"mergeMode"`
	AddMode              *bool         `json:"addMode"`
	GotoLineMode         *bool         `json:"gotoLineMode"`
	InitialStartup       *bool         `json:"initialStartup"`
	NoRecentEntry        *bool         `json:"noRecentEntry"`
	RemoteAuthority      *string       `json:"remoteAuthority"`
	Profile              interface{}   `json:"profile"` // IUserDataProfile
}

// IOpenEmptyConfiguration represents empty window open configuration
type IOpenEmptyConfiguration struct {
	IBaseOpenConfiguration
	RemoteAuthority *string     `json:"remoteAuthority"`
	Profile         interface{} `json:"profile"` // IUserDataProfile
}

// IWindowsMainService represents the main windows service interface
type IWindowsMainService interface {
	// ServiceBrand for dependency injection
	ServiceBrand() interface{}

	// Events
	OnDidChangeWindowsCount() basecommon.Event[*IWindowsCountChangedEvent]
	OnDidOpenWindow() basecommon.Event[windowelectronmain.ICodeWindow]
	OnDidSignalReadyWindow() basecommon.Event[windowelectronmain.ICodeWindow]
	OnDidMaximizeWindow() basecommon.Event[windowelectronmain.ICodeWindow]
	OnDidUnmaximizeWindow() basecommon.Event[windowelectronmain.ICodeWindow]
	OnDidChangeFullScreen() basecommon.Event[*struct {
		Window     windowelectronmain.ICodeWindow `json:"window"`
		Fullscreen bool                           `json:"fullscreen"`
	}]
	OnDidTriggerSystemContextMenu() basecommon.Event[*struct {
		Window windowelectronmain.ICodeWindow `json:"window"`
		X      int                            `json:"x"`
		Y      int                            `json:"y"`
	}]
	OnDidDestroyWindow() basecommon.Event[windowelectronmain.ICodeWindow]

	// Methods
	Open(openConfig *IOpenConfiguration) ([]windowelectronmain.ICodeWindow, error)
	OpenEmptyWindow(openConfig *IOpenEmptyConfiguration, options interface{}) ([]windowelectronmain.ICodeWindow, error) // IOpenEmptyWindowOptions
	OpenExtensionDevelopmentHostWindow(extensionDevelopmentPath []string, openConfig *IOpenConfiguration) ([]windowelectronmain.ICodeWindow, error)
	OpenExistingWindow(window windowelectronmain.ICodeWindow, openConfig *IOpenConfiguration)
	SendToFocused(channel string, args ...interface{})
	SendToOpeningWindow(channel string, args ...interface{})
	SendToAll(channel string, payload interface{}, windowIdsToIgnore []int)
	GetWindows() []windowelectronmain.ICodeWindow
	GetWindowCount() int
	GetFocusedWindow() windowelectronmain.ICodeWindow
	GetLastActiveWindow() windowelectronmain.ICodeWindow
	GetWindowByID(windowID int) windowelectronmain.ICodeWindow
	GetWindowByWebContents(webContents interface{}) windowelectronmain.ICodeWindow // electron.WebContents
}

// WindowsMainService implements IWindowsMainService
type WindowsMainService struct {
	windows          []windowelectronmain.ICodeWindow
	focusedWindow    windowelectronmain.ICodeWindow
	lastActiveWindow windowelectronmain.ICodeWindow

	// Events
	onDidChangeWindowsCount *basecommon.Emitter[*IWindowsCountChangedEvent]
	onDidOpenWindow         *basecommon.Emitter[windowelectronmain.ICodeWindow]
	onDidSignalReadyWindow  *basecommon.Emitter[windowelectronmain.ICodeWindow]
	onDidMaximizeWindow     *basecommon.Emitter[windowelectronmain.ICodeWindow]
	onDidUnmaximizeWindow   *basecommon.Emitter[windowelectronmain.ICodeWindow]
	onDidChangeFullScreen   *basecommon.Emitter[*struct {
		Window     windowelectronmain.ICodeWindow `json:"window"`
		Fullscreen bool                           `json:"fullscreen"`
	}]
	onDidTriggerSystemContextMenu *basecommon.Emitter[*struct {
		Window windowelectronmain.ICodeWindow `json:"window"`
		X      int                            `json:"x"`
		Y      int                            `json:"y"`
	}]
	onDidDestroyWindow *basecommon.Emitter[windowelectronmain.ICodeWindow]
}

// NewWindowsMainService creates a new windows main service
func NewWindowsMainService() *WindowsMainService {
	return &WindowsMainService{
		windows:                 make([]windowelectronmain.ICodeWindow, 0),
		onDidChangeWindowsCount: basecommon.NewEmitter[*IWindowsCountChangedEvent](),
		onDidOpenWindow:         basecommon.NewEmitter[windowelectronmain.ICodeWindow](),
		onDidSignalReadyWindow:  basecommon.NewEmitter[windowelectronmain.ICodeWindow](),
		onDidMaximizeWindow:     basecommon.NewEmitter[windowelectronmain.ICodeWindow](),
		onDidUnmaximizeWindow:   basecommon.NewEmitter[windowelectronmain.ICodeWindow](),
		onDidChangeFullScreen: basecommon.NewEmitter[*struct {
			Window     windowelectronmain.ICodeWindow `json:"window"`
			Fullscreen bool                           `json:"fullscreen"`
		}](),
		onDidTriggerSystemContextMenu: basecommon.NewEmitter[*struct {
			Window windowelectronmain.ICodeWindow `json:"window"`
			X      int                            `json:"x"`
			Y      int                            `json:"y"`
		}](),
		onDidDestroyWindow: basecommon.NewEmitter[windowelectronmain.ICodeWindow](),
	}
}

// ServiceBrand implements the service brand
func (s *WindowsMainService) ServiceBrand() interface{} {
	return "windowsMainService"
}

// OnDidChangeWindowsCount returns the windows count changed event
func (s *WindowsMainService) OnDidChangeWindowsCount() basecommon.Event[*IWindowsCountChangedEvent] {
	return s.onDidChangeWindowsCount.Event()
}

// OnDidOpenWindow returns the open window event
func (s *WindowsMainService) OnDidOpenWindow() basecommon.Event[windowelectronmain.ICodeWindow] {
	return s.onDidOpenWindow.Event()
}

// OnDidSignalReadyWindow returns the signal ready window event
func (s *WindowsMainService) OnDidSignalReadyWindow() basecommon.Event[windowelectronmain.ICodeWindow] {
	return s.onDidSignalReadyWindow.Event()
}

// OnDidMaximizeWindow returns the maximize window event
func (s *WindowsMainService) OnDidMaximizeWindow() basecommon.Event[windowelectronmain.ICodeWindow] {
	return s.onDidMaximizeWindow.Event()
}

// OnDidUnmaximizeWindow returns the unmaximize window event
func (s *WindowsMainService) OnDidUnmaximizeWindow() basecommon.Event[windowelectronmain.ICodeWindow] {
	return s.onDidUnmaximizeWindow.Event()
}

// OnDidChangeFullScreen returns the change full screen event
func (s *WindowsMainService) OnDidChangeFullScreen() basecommon.Event[*struct {
	Window     windowelectronmain.ICodeWindow `json:"window"`
	Fullscreen bool                           `json:"fullscreen"`
}] {
	return s.onDidChangeFullScreen.Event()
}

// OnDidTriggerSystemContextMenu returns the trigger system context menu event
func (s *WindowsMainService) OnDidTriggerSystemContextMenu() basecommon.Event[*struct {
	Window windowelectronmain.ICodeWindow `json:"window"`
	X      int                            `json:"x"`
	Y      int                            `json:"y"`
}] {
	return s.onDidTriggerSystemContextMenu.Event()
}

// OnDidDestroyWindow returns the destroy window event
func (s *WindowsMainService) OnDidDestroyWindow() basecommon.Event[windowelectronmain.ICodeWindow] {
	return s.onDidDestroyWindow.Event()
}

// Open opens windows with the given configuration
func (s *WindowsMainService) Open(openConfig *IOpenConfiguration) ([]windowelectronmain.ICodeWindow, error) {
	// Implementation would open windows based on configuration
	return []windowelectronmain.ICodeWindow{}, nil
}

// OpenEmptyWindow opens an empty window
func (s *WindowsMainService) OpenEmptyWindow(openConfig *IOpenEmptyConfiguration, options interface{}) ([]windowelectronmain.ICodeWindow, error) {
	// Implementation would open empty window
	return []windowelectronmain.ICodeWindow{}, nil
}

// OpenExtensionDevelopmentHostWindow opens an extension development host window
func (s *WindowsMainService) OpenExtensionDevelopmentHostWindow(extensionDevelopmentPath []string, openConfig *IOpenConfiguration) ([]windowelectronmain.ICodeWindow, error) {
	// Implementation would open extension development host window
	return []windowelectronmain.ICodeWindow{}, nil
}

// OpenExistingWindow opens an existing window with new configuration
func (s *WindowsMainService) OpenExistingWindow(window windowelectronmain.ICodeWindow, openConfig *IOpenConfiguration) {
	// Implementation would reconfigure existing window
}

// SendToFocused sends a message to the focused window
func (s *WindowsMainService) SendToFocused(channel string, args ...interface{}) {
	if s.focusedWindow != nil {
		s.focusedWindow.Send(channel, args...)
	}
}

// SendToOpeningWindow sends a message to the opening window
func (s *WindowsMainService) SendToOpeningWindow(channel string, args ...interface{}) {
	// Implementation would send to opening window
}

// SendToAll sends a message to all windows
func (s *WindowsMainService) SendToAll(channel string, payload interface{}, windowIdsToIgnore []int) {
	for _, window := range s.windows {
		shouldIgnore := false
		for _, ignoreID := range windowIdsToIgnore {
			if window.ID() == ignoreID {
				shouldIgnore = true
				break
			}
		}
		if !shouldIgnore {
			window.Send(channel, payload)
		}
	}
}

// GetWindows returns all windows
func (s *WindowsMainService) GetWindows() []windowelectronmain.ICodeWindow {
	return s.windows
}

// GetWindowCount returns the number of windows
func (s *WindowsMainService) GetWindowCount() int {
	return len(s.windows)
}

// GetFocusedWindow returns the focused window
func (s *WindowsMainService) GetFocusedWindow() windowelectronmain.ICodeWindow {
	return s.focusedWindow
}

// GetLastActiveWindow returns the last active window
func (s *WindowsMainService) GetLastActiveWindow() windowelectronmain.ICodeWindow {
	return s.lastActiveWindow
}

// GetWindowByID returns a window by its ID
func (s *WindowsMainService) GetWindowByID(windowID int) windowelectronmain.ICodeWindow {
	for _, window := range s.windows {
		if window.ID() == windowID {
			return window
		}
	}
	return nil
}

// GetWindowByWebContents returns a window by its web contents
func (s *WindowsMainService) GetWindowByWebContents(webContents interface{}) windowelectronmain.ICodeWindow {
	for _, window := range s.windows {
		if window.Matches(webContents) {
			return window
		}
	}
	return nil
}

// GetAllWindowsExcludingOffscreen returns all windows excluding offscreen windows
func GetAllWindowsExcludingOffscreen() []interface{} {
	// Implementation would filter out offscreen windows
	// This is a utility function that would interact with electron
	return []interface{}{}
}

// Service identifier for dependency injection
var IWindowsMainServiceID = instantiationcommon.CreateDecorator[IWindowsMainService]("windowsMainService")
