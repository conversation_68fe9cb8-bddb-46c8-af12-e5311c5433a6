/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	workspacecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/workspace/common"
)

// IBaseBackupInfo represents the base backup info interface
type IBaseBackupInfo struct {
	RemoteAuthority *string `json:"remoteAuthority,omitempty"`
}

// IWorkspaceBackupInfo represents workspace backup info that extends IBaseBackupInfo
type IWorkspaceBackupInfo struct {
	IBaseBackupInfo
	Workspace *workspacecommon.IWorkspaceIdentifier `json:"workspace"`
}

// IFolderBackupInfo represents folder backup info that extends IBaseBackupInfo
type IFolderBackupInfo struct {
	IBaseBackupInfo
	FolderUri *basecommon.URI `json:"folderUri"`
}

// IsFolderBackupInfo checks if the given backup info is a folder backup info
func IsFolderBackupInfo(curr interface{}) bool {
	if folderInfo, ok := curr.(*IFolderBackupInfo); ok {
		return folderInfo.FolderUri != nil
	}
	return false
}

// IsWorkspaceBackupInfo checks if the given backup info is a workspace backup info
func IsWorkspaceBackupInfo(curr interface{}) bool {
	if workspaceInfo, ok := curr.(*IWorkspaceBackupInfo); ok {
		return workspaceInfo.Workspace != nil
	}
	return false
}
