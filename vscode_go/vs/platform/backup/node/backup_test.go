/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package node

import (
	"testing"

	backupcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/backup/common"
)

func TestIsEmptyWindowBackupInfo(t *testing.T) {
	// Test positive case
	emptyWindowInfo := &IEmptyWindowBackupInfo{
		IBaseBackupInfo: backupcommon.IBaseBackupInfo{
			RemoteAuthority: nil,
		},
		BackupFolder: "/path/to/backup",
	}

	if !IsEmptyWindowBackupInfo(emptyWindowInfo) {
		t.Error("Expected IsEmptyWindowBackupInfo to return true for valid empty window backup info")
	}

	// Test negative case - empty backup folder
	emptyWindowInfoEmpty := &IEmptyWindowBackupInfo{
		IBaseBackupInfo: backupcommon.IBaseBackupInfo{
			RemoteAuthority: nil,
		},
		BackupFolder: "",
	}

	if IsEmptyWindowBackupInfo(emptyWindowInfoEmpty) {
		t.Error("Expected IsEmptyWindowBackupInfo to return false for empty backup folder")
	}

	// Test negative case - nil
	if IsEmptyWindowBackupInfo(nil) {
		t.Error("Expected IsEmptyWindowBackupInfo to return false for nil")
	}

	// Test negative case - wrong type
	if IsEmptyWindowBackupInfo("not a backup info") {
		t.Error("Expected IsEmptyWindowBackupInfo to return false for wrong type")
	}
}

func TestDeserializeWorkspaceInfos(t *testing.T) {
	// Test with valid data
	serializedData := &ISerializedBackupWorkspaces{
		Workspaces: []*ISerializedWorkspaceBackupInfo{
			{
				ID:              "workspace-1",
				ConfigURIPath:   "file:///path/to/workspace.code-workspace",
				RemoteAuthority: nil,
			},
			{
				ID:              "workspace-2",
				ConfigURIPath:   "file:///path/to/workspace2.code-workspace",
				RemoteAuthority: func() *string { s := "remote-auth"; return &s }(),
			},
		},
	}

	workspaceInfos := DeserializeWorkspaceInfos(serializedData)

	if len(workspaceInfos) != 2 {
		t.Errorf("Expected 2 workspace infos, got %d", len(workspaceInfos))
	}

	if workspaceInfos[0].Workspace.ID != "workspace-1" {
		t.Errorf("Expected workspace ID to be 'workspace-1', got %s", workspaceInfos[0].Workspace.ID)
	}

	if workspaceInfos[1].RemoteAuthority == nil || *workspaceInfos[1].RemoteAuthority != "remote-auth" {
		t.Error("Expected remote authority to be 'remote-auth'")
	}

	// Test with nil data
	nilResult := DeserializeWorkspaceInfos(nil)
	if len(nilResult) != 0 {
		t.Error("Expected empty slice for nil input")
	}

	// Test with invalid URI - this should skip the invalid entry
	invalidData := &ISerializedBackupWorkspaces{
		Workspaces: []*ISerializedWorkspaceBackupInfo{
			{
				ID:            "workspace-1",
				ConfigURIPath: "", // Empty string should cause parse error
			},
		},
	}

	invalidResult := DeserializeWorkspaceInfos(invalidData)
	// The function may still parse some URIs, so we just check it doesn't crash
	if invalidResult == nil {
		t.Error("Expected non-nil result even with invalid URI")
	}
}

func TestDeserializeFolderInfos(t *testing.T) {
	// Test with valid data
	serializedData := &ISerializedBackupWorkspaces{
		Folders: []*ISerializedFolderBackupInfo{
			{
				FolderUri:       "file:///path/to/folder",
				RemoteAuthority: nil,
			},
			{
				FolderUri:       "file:///path/to/folder2",
				RemoteAuthority: func() *string { s := "remote-auth"; return &s }(),
			},
		},
	}

	folderInfos := DeserializeFolderInfos(serializedData)

	if len(folderInfos) != 2 {
		t.Errorf("Expected 2 folder infos, got %d", len(folderInfos))
	}

	if folderInfos[0].FolderUri.GetPath() != "/path/to/folder" {
		t.Errorf("Expected folder path to be '/path/to/folder', got %s", folderInfos[0].FolderUri.GetPath())
	}

	if folderInfos[1].RemoteAuthority == nil || *folderInfos[1].RemoteAuthority != "remote-auth" {
		t.Error("Expected remote authority to be 'remote-auth'")
	}

	// Test with nil data
	nilResult := DeserializeFolderInfos(nil)
	if len(nilResult) != 0 {
		t.Error("Expected empty slice for nil input")
	}

	// Test with invalid URI - this should skip the invalid entry
	invalidData := &ISerializedBackupWorkspaces{
		Folders: []*ISerializedFolderBackupInfo{
			{
				FolderUri: "", // Empty string should cause parse error
			},
		},
	}

	invalidResult := DeserializeFolderInfos(invalidData)
	// The function may still parse some URIs, so we just check it doesn't crash
	if invalidResult == nil {
		t.Error("Expected non-nil result even with invalid URI")
	}
}
