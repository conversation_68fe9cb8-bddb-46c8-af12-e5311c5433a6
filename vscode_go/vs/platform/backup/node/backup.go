/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package node

import (
	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	backupcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/backup/common"
	workspacecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/workspace/common"
)

// IEmptyWindowBackupInfo represents empty window backup info that extends IBaseBackupInfo
type IEmptyWindowBackupInfo struct {
	backupcommon.IBaseBackupInfo
	BackupFolder string `json:"backupFolder"`
}

// IsEmptyWindowBackupInfo checks if the given object is an empty window backup info
func IsEmptyWindowBackupInfo(obj interface{}) bool {
	if candidate, ok := obj.(*IEmptyWindowBackupInfo); ok && candidate != nil {
		return candidate.BackupFolder != ""
	}
	return false
}

// ISerializedWorkspaceBackupInfo represents serialized workspace backup info
type ISerializedWorkspaceBackupInfo struct {
	ID              string  `json:"id"`
	ConfigURIPath   string  `json:"configURIPath"`
	RemoteAuthority *string `json:"remoteAuthority,omitempty"`
}

// DeserializeWorkspaceInfos deserializes workspace backup infos from serialized data
func DeserializeWorkspaceInfos(serializedBackupWorkspaces *ISerializedBackupWorkspaces) []*backupcommon.IWorkspaceBackupInfo {
	var workspaceBackupInfos []*backupcommon.IWorkspaceBackupInfo

	if serializedBackupWorkspaces != nil && serializedBackupWorkspaces.Workspaces != nil {
		for _, workspace := range serializedBackupWorkspaces.Workspaces {
			// Try to parse the URI
			configPath, err := basecommon.ParseURI(workspace.ConfigURIPath)
			if err != nil {
				// ignore URI parsing exceptions
				continue
			}

			workspaceBackupInfo := &backupcommon.IWorkspaceBackupInfo{
				IBaseBackupInfo: backupcommon.IBaseBackupInfo{
					RemoteAuthority: workspace.RemoteAuthority,
				},
				Workspace: &workspacecommon.IWorkspaceIdentifier{
					IBaseWorkspaceIdentifier: workspacecommon.IBaseWorkspaceIdentifier{
						ID: workspace.ID,
					},
					ConfigPath: configPath,
				},
			}
			workspaceBackupInfos = append(workspaceBackupInfos, workspaceBackupInfo)
		}
	}

	return workspaceBackupInfos
}

// ISerializedFolderBackupInfo represents serialized folder backup info
type ISerializedFolderBackupInfo struct {
	FolderUri       string  `json:"folderUri"`
	RemoteAuthority *string `json:"remoteAuthority,omitempty"`
}

// DeserializeFolderInfos deserializes folder backup infos from serialized data
func DeserializeFolderInfos(serializedBackupWorkspaces *ISerializedBackupWorkspaces) []*backupcommon.IFolderBackupInfo {
	var folderBackupInfos []*backupcommon.IFolderBackupInfo

	if serializedBackupWorkspaces != nil && serializedBackupWorkspaces.Folders != nil {
		for _, folder := range serializedBackupWorkspaces.Folders {
			// Try to parse the URI
			folderUri, err := basecommon.ParseURI(folder.FolderUri)
			if err != nil {
				// ignore URI parsing exceptions
				continue
			}

			folderBackupInfo := &backupcommon.IFolderBackupInfo{
				IBaseBackupInfo: backupcommon.IBaseBackupInfo{
					RemoteAuthority: folder.RemoteAuthority,
				},
				FolderUri: folderUri,
			}
			folderBackupInfos = append(folderBackupInfos, folderBackupInfo)
		}
	}

	return folderBackupInfos
}

// ISerializedEmptyWindowBackupInfo represents serialized empty window backup info
type ISerializedEmptyWindowBackupInfo struct {
	IEmptyWindowBackupInfo
}

// ILegacySerializedBackupWorkspaces represents legacy serialized backup workspaces
type ILegacySerializedBackupWorkspaces struct {
	RootURIWorkspaces    []*ISerializedWorkspaceBackupInfo   `json:"rootURIWorkspaces"`
	FolderWorkspaceInfos []*ISerializedFolderBackupInfo      `json:"folderWorkspaceInfos"`
	EmptyWorkspaceInfos  []*ISerializedEmptyWindowBackupInfo `json:"emptyWorkspaceInfos"`
}

// ISerializedBackupWorkspaces represents serialized backup workspaces
type ISerializedBackupWorkspaces struct {
	Workspaces   []*ISerializedWorkspaceBackupInfo   `json:"workspaces"`
	Folders      []*ISerializedFolderBackupInfo      `json:"folders"`
	EmptyWindows []*ISerializedEmptyWindowBackupInfo `json:"emptyWindows"`
}
