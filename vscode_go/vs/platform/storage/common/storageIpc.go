/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	userDataProfileCommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/userDataProfile/common"
	workspacecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/workspace/common"
)

// Key represents a storage key
type Key = string

// Value represents a storage value
type Value = string

// Item represents a key-value pair
type Item = [2]string // [Key, Value]

// IBaseSerializableStorageRequest represents a base serializable storage request
type IBaseSerializableStorageRequest struct {
	// Profile to correlate storage. Only used when no
	// workspace is provided. Can be undefined to denote
	// application scope.
	Profile *userDataProfileCommon.IUserDataProfile `json:"profile,omitempty"`

	// Workspace to correlate storage. Can be undefined to
	// denote application or profile scope depending on profile.
	Workspace workspacecommon.IAnyWorkspaceIdentifier `json:"workspace,omitempty"`

	// Additional payload for the request to perform.
	Payload interface{} `json:"payload,omitempty"`
}

// ISerializableUpdateRequest represents a serializable update request
type ISerializableUpdateRequest struct {
	IBaseSerializableStorageRequest
	Insert []Item `json:"insert,omitempty"`
	Delete []Key  `json:"delete,omitempty"`
}

// ISerializableItemsChangeEvent represents a serializable items change event
type ISerializableItemsChangeEvent struct {
	Changed []Item `json:"changed,omitempty"`
	Deleted []Key  `json:"deleted,omitempty"`
}
