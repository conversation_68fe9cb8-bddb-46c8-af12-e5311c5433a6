/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"encoding/json"
	"sync"
	"time"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
)

// Storage constants
const (
	IS_NEW_KEY = "__$__isNewStorageMarker"
	TARGET_KEY = "__$__targetStorageMarker"
)

// IKeyTargets represents key targets mapping
type IKeyTargets map[string]StorageTarget

// LoadKeyTargets loads key targets from storage
func LoadKeyTargets(storage IStorage) IKeyTargets {
	keysRaw := storage.GetString(TARGET_KEY, "")
	if keysRaw != "" {
		var keyTargets IKeyTargets
		if err := json.Unmarshal([]byte(keysRaw), &keyTargets); err == nil {
			return keyTargets
		}
	}
	return make(IKeyTargets)
}

// StorageScope represents the scope of storage
type StorageScope int

const (
	// StorageScopeApplication - The stored data will be scoped to all workspaces across all profiles
	StorageScopeApplication StorageScope = -1
	// StorageScopeProfile - The stored data will be scoped to all workspaces of the same profile
	StorageScopeProfile StorageScope = 0
	// StorageScopeWorkspace - The stored data will be scoped to the current workspace
	StorageScopeWorkspace StorageScope = 1
)

// String returns the string representation of StorageScope
func (s StorageScope) String() string {
	switch s {
	case StorageScopeApplication:
		return "APPLICATION"
	case StorageScopeProfile:
		return "PROFILE"
	case StorageScopeWorkspace:
		return "WORKSPACE"
	default:
		return "UNKNOWN"
	}
}

// StorageTarget represents the target of storage
type StorageTarget int

const (
	StorageTargetUser StorageTarget = iota
	StorageTargetMachine
)

// String returns the string representation of StorageTarget
func (s StorageTarget) String() string {
	switch s {
	case StorageTargetUser:
		return "USER"
	case StorageTargetMachine:
		return "MACHINE"
	default:
		return "UNKNOWN"
	}
}

// IStorageChangeEvent represents a storage change event
type IStorageChangeEvent struct {
	Scope  StorageScope  `json:"scope"`
	Key    string        `json:"key"`
	Value  interface{}   `json:"value,omitempty"`
	Target StorageTarget `json:"target"`
}

// IStorageValueChangeEvent represents a storage value change event
type IStorageValueChangeEvent struct {
	IStorageChangeEvent
	External bool `json:"external"`
}

// IStorageTargetChangeEvent represents a storage target change event
type IStorageTargetChangeEvent struct {
	Scope StorageScope `json:"scope"`
}

// IStorage represents the storage interface
type IStorage interface {
	// Event subscriptions
	OnDidChangeValue() basecommon.Event[*IStorageValueChangeEvent]
	OnDidChangeTarget() basecommon.Event[*IStorageTargetChangeEvent]
	OnWillSaveState() basecommon.Event[*IStorageValueChangeEvent]

	// Storage operations
	Get(key string, fallbackValue interface{}) interface{}
	GetBoolean(key string, fallbackValue bool) bool
	GetNumber(key string, fallbackValue float64) float64
	GetString(key string, fallbackValue string) string
	GetObject(key string, fallbackValue interface{}) interface{}
	Set(key string, value interface{}, target StorageTarget) error
	Delete(key string) error
	Has(key string) bool
	Keys() []string
	Size() int
	Flush() error
	FlushDelayed() error
	IsNew() bool
	Close() error
}

// IStorageDatabase represents the storage database interface
type IStorageDatabase interface {
	GetItems() (map[string]interface{}, error)
	UpdateItems(request *IUpdateRequest) error
	Close() error
	CheckIntegrity(full bool) (string, error)
}

// IUpdateRequest represents an update request
type IUpdateRequest struct {
	Insert map[string]interface{} `json:"insert,omitempty"`
	Delete []string               `json:"delete,omitempty"`
}

// IStorageItemsChangeEvent represents storage items change event
type IStorageItemsChangeEvent struct {
	Changed map[string]interface{} `json:"changed,omitempty"`
	Deleted []string               `json:"deleted,omitempty"`
}

// SimpleScheduler represents a simple scheduler for delayed operations
type SimpleScheduler struct {
	fn       func()
	delay    time.Duration
	timer    *time.Timer
	mutex    sync.Mutex
	canceled bool
}

// NewSimpleScheduler creates a new simple scheduler
func NewSimpleScheduler(fn func(), delay time.Duration) *SimpleScheduler {
	return &SimpleScheduler{
		fn:    fn,
		delay: delay,
	}
}

// Schedule schedules the function to run after the delay
func (s *SimpleScheduler) Schedule() {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if s.canceled {
		return
	}

	if s.timer != nil {
		s.timer.Stop()
	}

	s.timer = time.AfterFunc(s.delay, func() {
		s.mutex.Lock()
		defer s.mutex.Unlock()
		if !s.canceled {
			s.fn()
		}
	})
}

// Cancel cancels the scheduled function
func (s *SimpleScheduler) Cancel() {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	s.canceled = true
	if s.timer != nil {
		s.timer.Stop()
		s.timer = nil
	}
}

// Storage represents the main storage implementation
type Storage struct {
	scope               StorageScope
	target              StorageTarget
	database            IStorageDatabase
	cache               map[string]interface{}
	isNew               bool
	flushDelayScheduler *SimpleScheduler

	// Event emitters
	onDidChangeValue  *basecommon.Emitter[*IStorageValueChangeEvent]
	onDidChangeTarget *basecommon.Emitter[*IStorageTargetChangeEvent]
	onWillSaveState   *basecommon.Emitter[*IStorageValueChangeEvent]

	// Mutex for thread safety
	mutex sync.RWMutex
}

// NewStorage creates a new storage instance
func NewStorage(database IStorageDatabase, scope StorageScope, target StorageTarget) (*Storage, error) {
	storage := &Storage{
		scope:             scope,
		target:            target,
		database:          database,
		cache:             make(map[string]interface{}),
		isNew:             true,
		onDidChangeValue:  basecommon.NewEmitter[*IStorageValueChangeEvent](),
		onDidChangeTarget: basecommon.NewEmitter[*IStorageTargetChangeEvent](),
		onWillSaveState:   basecommon.NewEmitter[*IStorageValueChangeEvent](),
	}

	// Create scheduler after storage is created
	storage.flushDelayScheduler = NewSimpleScheduler(func() { storage.doFlush() }, 100*time.Millisecond)

	// Load initial data
	if err := storage.loadFromDatabase(); err != nil {
		return nil, err
	}

	return storage, nil
}

// OnDidChangeValue returns the event for value changes
func (s *Storage) OnDidChangeValue() basecommon.Event[*IStorageValueChangeEvent] {
	return s.onDidChangeValue.Event()
}

// OnDidChangeTarget returns the event for target changes
func (s *Storage) OnDidChangeTarget() basecommon.Event[*IStorageTargetChangeEvent] {
	return s.onDidChangeTarget.Event()
}

// OnWillSaveState returns the event for save state
func (s *Storage) OnWillSaveState() basecommon.Event[*IStorageValueChangeEvent] {
	return s.onWillSaveState.Event()
}

// Get retrieves a value from storage
func (s *Storage) Get(key string, fallbackValue interface{}) interface{} {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	if value, exists := s.cache[key]; exists {
		return value
	}
	return fallbackValue
}

// GetBoolean retrieves a boolean value from storage
func (s *Storage) GetBoolean(key string, fallbackValue bool) bool {
	value := s.Get(key, fallbackValue)
	if boolValue, ok := value.(bool); ok {
		return boolValue
	}
	return fallbackValue
}

// GetNumber retrieves a numeric value from storage
func (s *Storage) GetNumber(key string, fallbackValue float64) float64 {
	value := s.Get(key, fallbackValue)
	switch v := value.(type) {
	case float64:
		return v
	case float32:
		return float64(v)
	case int:
		return float64(v)
	case int64:
		return float64(v)
	default:
		return fallbackValue
	}
}

// GetString retrieves a string value from storage
func (s *Storage) GetString(key string, fallbackValue string) string {
	value := s.Get(key, fallbackValue)
	if stringValue, ok := value.(string); ok {
		return stringValue
	}
	return fallbackValue
}

// GetObject retrieves an object value from storage
func (s *Storage) GetObject(key string, fallbackValue interface{}) interface{} {
	value := s.Get(key, fallbackValue)
	if value == nil {
		return fallbackValue
	}
	return value
}

// Set stores a value in storage
func (s *Storage) Set(key string, value interface{}, target StorageTarget) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	// Check if value actually changed
	oldValue, existed := s.cache[key]
	if existed && s.valuesEqual(oldValue, value) {
		return nil
	}

	// Update cache
	s.cache[key] = value

	// Emit change event
	event := &IStorageValueChangeEvent{
		IStorageChangeEvent: IStorageChangeEvent{
			Scope:  s.scope,
			Key:    key,
			Value:  value,
			Target: target,
		},
		External: false,
	}
	s.onDidChangeValue.Fire(event)

	// Schedule flush
	s.flushDelayScheduler.Schedule()

	return nil
}

// Delete removes a key from storage
func (s *Storage) Delete(key string) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if _, exists := s.cache[key]; !exists {
		return nil // Key doesn't exist, nothing to do
	}

	delete(s.cache, key)

	// Emit change event
	event := &IStorageValueChangeEvent{
		IStorageChangeEvent: IStorageChangeEvent{
			Scope:  s.scope,
			Key:    key,
			Value:  nil,
			Target: s.target,
		},
		External: false,
	}
	s.onDidChangeValue.Fire(event)

	// Schedule flush
	s.flushDelayScheduler.Schedule()

	return nil
}

// Has checks if a key exists in storage
func (s *Storage) Has(key string) bool {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	_, exists := s.cache[key]
	return exists
}

// Keys returns all keys in storage
func (s *Storage) Keys() []string {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	keys := make([]string, 0, len(s.cache))
	for key := range s.cache {
		keys = append(keys, key)
	}
	return keys
}

// Size returns the number of items in storage
func (s *Storage) Size() int {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	return len(s.cache)
}

// Flush forces a flush of pending changes
func (s *Storage) Flush() error {
	return s.doFlush()
}

// FlushDelayed schedules a delayed flush
func (s *Storage) FlushDelayed() error {
	s.flushDelayScheduler.Schedule()
	return nil
}

// IsNew returns whether this is a new storage instance
func (s *Storage) IsNew() bool {
	return s.isNew
}

// Close closes the storage
func (s *Storage) Close() error {
	if s.flushDelayScheduler != nil {
		s.flushDelayScheduler.Cancel()
	}

	// Final flush
	if err := s.doFlush(); err != nil {
		return err
	}

	// Close database
	if s.database != nil {
		return s.database.Close()
	}

	return nil
}

// doFlush performs the actual flush operation
func (s *Storage) doFlush() error {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	if len(s.cache) == 0 {
		return nil
	}

	// Emit will save state event
	for key, value := range s.cache {
		event := &IStorageValueChangeEvent{
			IStorageChangeEvent: IStorageChangeEvent{
				Scope:  s.scope,
				Key:    key,
				Value:  value,
				Target: s.target,
			},
			External: false,
		}
		s.onWillSaveState.Fire(event)
	}

	// Update database
	request := &IUpdateRequest{
		Insert: s.cache,
	}

	return s.database.UpdateItems(request)
}

// loadFromDatabase loads initial data from the database
func (s *Storage) loadFromDatabase() error {
	items, err := s.database.GetItems()
	if err != nil {
		return err
	}

	s.mutex.Lock()
	defer s.mutex.Unlock()

	s.cache = items
	s.isNew = len(items) == 0

	return nil
}

// valuesEqual compares two values for equality
func (s *Storage) valuesEqual(a, b interface{}) bool {
	if a == b {
		return true
	}

	// For complex types, compare JSON representation
	aJSON, aErr := json.Marshal(a)
	bJSON, bErr := json.Marshal(b)

	if aErr != nil || bErr != nil {
		return false
	}

	return string(aJSON) == string(bJSON)
}

// InMemoryStorageDatabase implements IStorageDatabase using in-memory storage
type InMemoryStorageDatabase struct {
	data  map[string]interface{}
	mutex sync.RWMutex
}

// NewInMemoryStorageDatabase creates a new in-memory storage database
func NewInMemoryStorageDatabase() *InMemoryStorageDatabase {
	return &InMemoryStorageDatabase{
		data: make(map[string]interface{}),
	}
}

// GetItems retrieves all items from memory
func (idb *InMemoryStorageDatabase) GetItems() (map[string]interface{}, error) {
	idb.mutex.RLock()
	defer idb.mutex.RUnlock()

	// Return a copy of the data
	result := make(map[string]interface{})
	for key, value := range idb.data {
		result[key] = value
	}
	return result, nil
}

// UpdateItems updates items in memory
func (idb *InMemoryStorageDatabase) UpdateItems(request *IUpdateRequest) error {
	idb.mutex.Lock()
	defer idb.mutex.Unlock()

	// Insert/update items
	if request.Insert != nil {
		for key, value := range request.Insert {
			idb.data[key] = value
		}
	}

	// Delete items
	if request.Delete != nil {
		for _, key := range request.Delete {
			delete(idb.data, key)
		}
	}

	return nil
}

// Close closes the in-memory database (no-op)
func (idb *InMemoryStorageDatabase) Close() error {
	return nil
}

// CheckIntegrity checks the integrity of the in-memory database
func (idb *InMemoryStorageDatabase) CheckIntegrity(full bool) (string, error) {
	idb.mutex.RLock()
	defer idb.mutex.RUnlock()

	if full {
		return "In-memory database integrity check passed. Items: " + string(rune(len(idb.data))), nil
	}
	return "ok", nil
}
