package common

import "sync"

// None represents a disposable that does nothing
var None IDisposable = &noneDisposable{}

// noneDisposable is a disposable that does nothing
type noneDisposable struct{}

func (n *noneDisposable) Dispose() {
	// Do nothing
}

// IDisposable is the interface for a disposable object.
type IDisposable interface {
	Dispose()
}

// Disposable is a base class for disposable objects.
type Disposable struct {
	mu         sync.Mutex
	isDisposed bool
	toDispose  []IDisposable
}

// NewDisposable creates a new Disposable.
func NewDisposable() *Disposable {
	return &Disposable{}
}

// Dispose disposes the object.
func (d *Disposable) Dispose() {
	d.mu.Lock()
	defer d.mu.Unlock()

	if d.isDisposed {
		return
	}

	d.isDisposed = true
	for _, disposable := range d.toDispose {
		disposable.Dispose()
	}
	d.toDispose = nil
}

// Register registers a disposable to be disposed when this object is disposed.
func (d *Disposable) Register(disposable IDisposable) IDisposable {
	d.mu.Lock()
	defer d.mu.Unlock()

	if d.isDisposed {
		disposable.Dispose()
	} else {
		d.toDispose = append(d.toDispose, disposable)
	}

	return disposable
}

// DisposableStore is a store for disposables.
type DisposableStore struct {
	*Disposable
}

// NewDisposableStore creates a new DisposableStore.
func NewDisposableStore() *DisposableStore {
	return &DisposableStore{
		Disposable: NewDisposable(),
	}
}

// Add adds a disposable to the store.
func (ds *DisposableStore) Add(disposable IDisposable) IDisposable {
	return ds.Register(disposable)
}

// Clear clears the store and disposes all disposables.
func (ds *DisposableStore) Clear() {
	ds.mu.Lock()
	defer ds.mu.Unlock()

	for _, disposable := range ds.toDispose {
		disposable.Dispose()
	}
	ds.toDispose = nil
}

// ToDisposable converts a function to a disposable.
func ToDisposable(fn func()) IDisposable {
	return &disposableFunc{fn: fn}
}

type disposableFunc struct {
	fn func()
}

func (d *disposableFunc) Dispose() {
	if d.fn != nil {
		d.fn()
		d.fn = nil
	}
}

// DisposableMap is a map that manages the lifecycle of the values that it stores.
type DisposableMap[K comparable, V IDisposable] struct {
	mu    sync.Mutex
	store map[K]V
}

// NewDisposableMap creates a new DisposableMap.
func NewDisposableMap[K comparable, V IDisposable]() *DisposableMap[K, V] {
	return &DisposableMap[K, V]{
		store: make(map[K]V),
	}
}

// Set sets a value in the map.
func (dm *DisposableMap[K, V]) Set(key K, value V) {
	dm.mu.Lock()
	defer dm.mu.Unlock()

	if oldValue, ok := dm.store[key]; ok {
		oldValue.Dispose()
	}
	dm.store[key] = value
}

// Get gets a value from the map.
func (dm *DisposableMap[K, V]) Get(key K) V {
	dm.mu.Lock()
	defer dm.mu.Unlock()

	return dm.store[key]
}

// Delete deletes a value from the map.
func (dm *DisposableMap[K, V]) Delete(key K) {
	dm.mu.Lock()
	defer dm.mu.Unlock()

	if oldValue, ok := dm.store[key]; ok {
		oldValue.Dispose()
		delete(dm.store, key)
	}
}

// Dispose disposes the map.
func (dm *DisposableMap[K, V]) Dispose() {
	dm.mu.Lock()
	defer dm.mu.Unlock()

	for _, value := range dm.store {
		value.Dispose()
	}
	dm.store = make(map[K]V)
}

// DisposableFunc is a function that acts as a disposable.
type DisposableFunc = *disposableFunc
