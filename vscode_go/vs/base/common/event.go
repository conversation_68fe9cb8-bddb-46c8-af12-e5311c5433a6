/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"fmt"
	"sync"
	"sync/atomic"
	"time"
)

// EventHandler represents a function that handles events
type EventHandler[T any] func(T)

// Event represents an event that can be subscribed to
type Event[T any] interface {
	// Subscribe to the event with a handler function
	Subscribe(handler EventHandler[T]) IDisposable
	// SubscribeOnce subscribes to the event but only fires once
	SubscribeOnce(handler <PERSON><PERSON><PERSON><PERSON>[T]) IDisposable
}

// Listener represents an event listener
type Listener[T any] struct {
	id      uint64
	handler EventHandler[T]
	once    bool
}

// Emitter implements Event and provides event emission capabilities
type Emitter[T any] struct {
	listeners    map[uint64]*Listener[T]
	mu           sync.RWMutex
	disposed     bool
	nextID       uint64
	hasListeners bool

	// Options
	onWillAddFirstListener  func()
	onDidAddFirstListener   func()
	onDidAddListener        func()
	onWillRemoveListener    func()
	onDidRemoveLastListener func()
	onListenerError         func(error)
	leakWarningThreshold    int
}

// EmitterOptions configures an emitter
type EmitterOptions struct {
	OnWillAddFirstListener  func()
	OnDidAddFirstListener   func()
	OnDidAddListener        func()
	OnWillRemoveListener    func()
	OnDidRemoveLastListener func()
	OnListenerError         func(error)
	LeakWarningThreshold    int
}

// NewEmitter creates a new emitter
func NewEmitter[T any](options ...EmitterOptions) *Emitter[T] {
	e := &Emitter[T]{
		listeners:            make(map[uint64]*Listener[T]),
		leakWarningThreshold: 30, // Default threshold
	}

	if len(options) > 0 {
		opt := options[0]
		e.onWillAddFirstListener = opt.OnWillAddFirstListener
		e.onDidAddFirstListener = opt.OnDidAddFirstListener
		e.onDidAddListener = opt.OnDidAddListener
		e.onWillRemoveListener = opt.OnWillRemoveListener
		e.onDidRemoveLastListener = opt.OnDidRemoveLastListener
		e.onListenerError = opt.OnListenerError
		if opt.LeakWarningThreshold > 0 {
			e.leakWarningThreshold = opt.LeakWarningThreshold
		}
	}

	return e
}

// Subscribe adds a listener to the event
func (e *Emitter[T]) Subscribe(handler EventHandler[T]) IDisposable {
	return e.addListener(handler, false)
}

// SubscribeOnce adds a one-time listener to the event
func (e *Emitter[T]) SubscribeOnce(handler EventHandler[T]) IDisposable {
	return e.addListener(handler, true)
}

// Event returns the event interface for this emitter
func (e *Emitter[T]) Event() Event[T] {
	return e
}

func (e *Emitter[T]) addListener(handler EventHandler[T], once bool) IDisposable {
	if handler == nil {
		return None
	}

	e.mu.Lock()
	defer e.mu.Unlock()

	if e.disposed {
		return None
	}

	// Call onWillAddFirstListener if this is the first listener
	if !e.hasListeners && e.onWillAddFirstListener != nil {
		e.onWillAddFirstListener()
	}

	id := atomic.AddUint64(&e.nextID, 1)
	listener := &Listener[T]{
		id:      id,
		handler: handler,
		once:    once,
	}

	e.listeners[id] = listener

	// Update hasListeners flag
	hadListeners := e.hasListeners
	e.hasListeners = len(e.listeners) > 0

	// Call onDidAddFirstListener if this was the first listener
	if !hadListeners && e.hasListeners && e.onDidAddFirstListener != nil {
		e.onDidAddFirstListener()
	}

	// Call onDidAddListener
	if e.onDidAddListener != nil {
		e.onDidAddListener()
	}

	// Check for potential listener leaks
	if len(e.listeners) > e.leakWarningThreshold {
		// In a real implementation, this would use proper logging
		// For now, we'll just ignore the warning
	}

	// Return disposable to remove the listener
	return ToDisposable(func() {
		e.removeListener(id)
	})
}

func (e *Emitter[T]) removeListener(id uint64) {
	e.mu.Lock()
	defer e.mu.Unlock()

	if e.disposed {
		return
	}

	if _, exists := e.listeners[id]; !exists {
		return
	}

	// Call onWillRemoveListener
	if e.onWillRemoveListener != nil {
		e.onWillRemoveListener()
	}

	delete(e.listeners, id)

	// Update hasListeners flag
	hadListeners := e.hasListeners
	e.hasListeners = len(e.listeners) > 0

	// Call onDidRemoveLastListener if this was the last listener
	if hadListeners && !e.hasListeners && e.onDidRemoveLastListener != nil {
		e.onDidRemoveLastListener()
	}
}

// Fire emits an event to all listeners
func (e *Emitter[T]) Fire(event T) {
	e.mu.RLock()
	if e.disposed || len(e.listeners) == 0 {
		e.mu.RUnlock()
		return
	}

	// Create a copy of listeners to avoid holding the lock during event delivery
	listeners := make([]*Listener[T], 0, len(e.listeners))
	for _, listener := range e.listeners {
		listeners = append(listeners, listener)
	}
	e.mu.RUnlock()

	// Deliver events without holding the lock
	for _, listener := range listeners {
		e.deliverEvent(listener, event)
	}

	// Remove one-time listeners
	e.mu.Lock()
	for id, listener := range e.listeners {
		if listener.once {
			delete(e.listeners, id)
		}
	}

	// Update hasListeners flag
	hadListeners := e.hasListeners
	e.hasListeners = len(e.listeners) > 0

	// Call onDidRemoveLastListener if we removed the last listener
	if hadListeners && !e.hasListeners && e.onDidRemoveLastListener != nil {
		e.onDidRemoveLastListener()
	}
	e.mu.Unlock()
}

func (e *Emitter[T]) deliverEvent(listener *Listener[T], event T) {
	defer func() {
		if r := recover(); r != nil {
			if e.onListenerError != nil {
				if err, ok := r.(error); ok {
					e.onListenerError(err)
				} else {
					e.onListenerError(NewError("listener panic: %v", r))
				}
			}
		}
	}()

	listener.handler(event)
}

// HasListeners returns true if the emitter has any listeners
func (e *Emitter[T]) HasListeners() bool {
	e.mu.RLock()
	defer e.mu.RUnlock()
	return e.hasListeners
}

// ListenerCount returns the number of listeners
func (e *Emitter[T]) ListenerCount() int {
	e.mu.RLock()
	defer e.mu.RUnlock()
	return len(e.listeners)
}

// Dispose disposes the emitter and removes all listeners
func (e *Emitter[T]) Dispose() {
	e.mu.Lock()
	defer e.mu.Unlock()

	if e.disposed {
		return
	}

	e.disposed = true
	e.listeners = make(map[uint64]*Listener[T])
	e.hasListeners = false
}

// IsDisposed returns true if the emitter has been disposed
func (e *Emitter[T]) IsDisposed() bool {
	e.mu.RLock()
	defer e.mu.RUnlock()
	return e.disposed
}

// EventMultiplexer combines multiple events into one
type EventMultiplexer[T any] struct {
	emitter      *Emitter[T]
	events       []Event[T]
	disposables  []IDisposable
	hasListeners bool
	mu           sync.Mutex
}

// NewEventMultiplexer creates a new event multiplexer
func NewEventMultiplexer[T any]() *EventMultiplexer[T] {
	return &EventMultiplexer[T]{
		emitter:     NewEmitter[T](),
		events:      make([]Event[T], 0),
		disposables: make([]IDisposable, 0),
	}
}

// Event returns the multiplexed event
func (em *EventMultiplexer[T]) Event() Event[T] {
	return em.emitter
}

// Add adds an event to the multiplexer
func (em *EventMultiplexer[T]) Add(event Event[T]) IDisposable {
	em.mu.Lock()
	defer em.mu.Unlock()

	em.events = append(em.events, event)

	var disposable IDisposable = None
	if em.hasListeners {
		disposable = event.Subscribe(func(e T) {
			em.emitter.Fire(e)
		})
		em.disposables = append(em.disposables, disposable)
	} else {
		em.disposables = append(em.disposables, None)
	}

	index := len(em.events) - 1
	return ToDisposable(func() {
		em.remove(index)
	})
}

func (em *EventMultiplexer[T]) remove(index int) {
	em.mu.Lock()
	defer em.mu.Unlock()

	if index < 0 || index >= len(em.events) {
		return
	}

	// Dispose the subscription
	em.disposables[index].Dispose()

	// Remove from slices
	em.events = append(em.events[:index], em.events[index+1:]...)
	em.disposables = append(em.disposables[:index], em.disposables[index+1:]...)
}

// SetHasListeners sets whether the multiplexer has listeners
func (em *EventMultiplexer[T]) SetHasListeners(hasListeners bool) {
	em.mu.Lock()
	defer em.mu.Unlock()

	if em.hasListeners == hasListeners {
		return
	}

	em.hasListeners = hasListeners

	if hasListeners {
		// Subscribe to all events
		for i, event := range em.events {
			if em.disposables[i] == None {
				em.disposables[i] = event.Subscribe(func(e T) {
					em.emitter.Fire(e)
				})
			}
		}
	} else {
		// Unsubscribe from all events
		for i, disposable := range em.disposables {
			disposable.Dispose()
			em.disposables[i] = None
		}
	}
}

// Dispose disposes the multiplexer
func (em *EventMultiplexer[T]) Dispose() {
	em.mu.Lock()
	defer em.mu.Unlock()

	for _, disposable := range em.disposables {
		disposable.Dispose()
	}

	em.emitter.Dispose()
	em.events = nil
	em.disposables = nil
}

// EventRelay relays events from one source to another
type EventRelay[T any] struct {
	emitter         *Emitter[T]
	input           Event[T]
	inputDisposable IDisposable
	listening       bool
	mu              sync.Mutex
}

// NewEventRelay creates a new event relay
func NewEventRelay[T any]() *EventRelay[T] {
	return &EventRelay[T]{
		emitter:         NewEmitter[T](),
		inputDisposable: None,
	}
}

// Event returns the relayed event
func (er *EventRelay[T]) Event() Event[T] {
	return er.emitter
}

// SetInput sets the input event to relay
func (er *EventRelay[T]) SetInput(input Event[T]) {
	er.mu.Lock()
	defer er.mu.Unlock()

	er.input = input

	if er.listening {
		er.inputDisposable.Dispose()
		er.inputDisposable = None

		if input != nil {
			er.inputDisposable = input.Subscribe(func(e T) {
				er.emitter.Fire(e)
			})
		}
	}
}

// SetListening sets whether the relay is listening
func (er *EventRelay[T]) SetListening(listening bool) {
	er.mu.Lock()
	defer er.mu.Unlock()

	if er.listening == listening {
		return
	}

	er.listening = listening

	if listening && er.input != nil {
		er.inputDisposable = er.input.Subscribe(func(e T) {
			er.emitter.Fire(e)
		})
	} else {
		er.inputDisposable.Dispose()
		er.inputDisposable = None
	}
}

// Dispose disposes the relay
func (er *EventRelay[T]) Dispose() {
	er.mu.Lock()
	defer er.mu.Unlock()

	er.inputDisposable.Dispose()
	er.emitter.Dispose()
}

// Event utility functions

// Once creates an event that fires only once
func Once[T any](event Event[T]) Event[T] {
	emitter := NewEmitter[T]()
	var disposable IDisposable

	emitter.onWillAddFirstListener = func() {
		disposable = event.SubscribeOnce(func(e T) {
			emitter.Fire(e)
		})
	}

	emitter.onDidRemoveLastListener = func() {
		if disposable != nil {
			disposable.Dispose()
		}
	}

	return emitter
}

// EventFilter creates an event that only fires when the filter condition is met
func EventFilter[T any](event Event[T], filter func(T) bool) Event[T] {
	emitter := NewEmitter[T]()
	var disposable IDisposable

	emitter.onWillAddFirstListener = func() {
		disposable = event.Subscribe(func(e T) {
			if filter(e) {
				emitter.Fire(e)
			}
		})
	}

	emitter.onDidRemoveLastListener = func() {
		if disposable != nil {
			disposable.Dispose()
		}
	}

	return emitter
}

// EventMap creates an event that transforms values using a mapping function
func EventMap[T, U any](event Event[T], mapper func(T) U) Event[U] {
	emitter := NewEmitter[U]()
	var disposable IDisposable

	emitter.onWillAddFirstListener = func() {
		disposable = event.Subscribe(func(e T) {
			emitter.Fire(mapper(e))
		})
	}

	emitter.onDidRemoveLastListener = func() {
		if disposable != nil {
			disposable.Dispose()
		}
	}

	return emitter
}

// Any creates an event that fires when any of the input events fire
func Any[T any](events ...Event[T]) Event[T] {
	emitter := NewEmitter[T]()
	var disposables []IDisposable

	emitter.onWillAddFirstListener = func() {
		disposables = make([]IDisposable, len(events))
		for i, event := range events {
			disposables[i] = event.Subscribe(func(e T) {
				emitter.Fire(e)
			})
		}
	}

	emitter.onDidRemoveLastListener = func() {
		for _, disposable := range disposables {
			if disposable != nil {
				disposable.Dispose()
			}
		}
		disposables = nil
	}

	return emitter
}

// Signal converts any event to a void event (signal)
func Signal[T any](event Event[T]) Event[struct{}] {
	return EventMap[T, struct{}](event, func(T) struct{} { return struct{}{} })
}

// None is an event that never fires
func EventNone[T any]() Event[T] {
	return &noneEvent[T]{}
}

type noneEvent[T any] struct{}

func (ne *noneEvent[T]) Subscribe(handler EventHandler[T]) IDisposable {
	return None
}

func (ne *noneEvent[T]) SubscribeOnce(handler EventHandler[T]) IDisposable {
	return None
}

// Debounce creates an event that debounces the input event
func Debounce[T any](event Event[T], delay time.Duration, merge func(T, T) T) Event[T] {
	emitter := NewEmitter[T]()
	var disposable IDisposable

	emitter.onWillAddFirstListener = func() {
		var timer *time.Timer
		var lastValue T
		var hasValue bool

		disposable = event.Subscribe(func(e T) {
			if hasValue {
				lastValue = merge(lastValue, e)
			} else {
				lastValue = e
				hasValue = true
			}

			if timer != nil {
				timer.Stop()
			}

			timer = time.AfterFunc(delay, func() {
				if hasValue {
					emitter.Fire(lastValue)
					hasValue = false
				}
			})
		})
	}

	emitter.onDidRemoveLastListener = func() {
		if disposable != nil {
			disposable.Dispose()
		}
	}

	return emitter
}

// NewError creates a new error with formatting
func NewError(format string, args ...interface{}) error {
	return fmt.Errorf(format, args...)
}
